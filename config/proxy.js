/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-05 10:45:05
 * @FilePath: /zcy-announcement-v2-front/config/proxy.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: httpss://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 接口代理转发
 * 针对多个域名，分别进行接口转发
 */

const domainTable = {
  test: 'test.zcygov.cn',
  staging: 'staging.zcygov.cn',
  shanghaiStaging: 'staging-shanghai.cai-inc.com',
};

const domain = domainTable.test;

const user = {
  // account: 'ahcgdw', // 运营
  // account: 'sxbjcgdw', // 运营
  // account: 'nycgdw02',
  // account: 'binjiangcgjg', // 滨江监管
  // account: '************',
  account: '************',
  // account: 'xjbjczjgyh',
  // account: 'nycgdw02',
  // account: 'csqcgdw03',
  // account: 'csqcgdw01',
  password: 'ZFCGuser@123456',
  // password: 'Zfcg@123456',
  // password: 'zcy123456',
};

module.exports = {
  domain,
  useMockStatusCode: [404], // number[]
  rules: {
    shuzhi: 
    {
      host: 'http://edm.test.cai-inc.com', // 七里
      urls: [
        '/api/opPlatform/*',
      ],
    },
    paas: {
      host: 'https://paas.cai-inc.com',
      urls: [
        '/api/form/*',
      ],
    },
    // 用户
    uaa: {
      host: `https://login.${domain}`,
      urls: [],
    },
    middle: {
      host: `https://middle.${domain}`,
      useMock: [
        // '/announcement/api/project/announcementProjectList',
        // '/api/biz-bidding/doctor-manhattan/project-manage/sync-metadata',
        // '/announcement/sensitive/detailAnnouncementSensitive',
        // '/announcement/api/small/listSelfAnnouncement',
        // '/announcement/api/small/showExportButton',
        // '/announcement/api/small/exportAnnouncementInfo',
        // '/announcement/api/small/pollingAnnouncementInfo',
        // '/api/opPlatform/prodOperation/announcement/bookmark/list',
        // '/api/opPlatform/prodOperation/announcement/bookmark/save',
        // '/api/opPlatform/prodOperation/announcement/stdlib/detail',
        // '/api/opPlatform/prodOperation/announcement/dict',
        // '/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark',
        // '/api/opPlatform/prodOperation/announcement/stdlib/showUsedInfo',
        // '/api/opPlatform/prodOperation/announcement/bookmark/detail',
        // '/api/opPlatform/prodOperation/announcement/bookmark/enable',
        // '/api/opPlatform/prodOperation/announcement/bookmark/disable',
        // '/api/opPlatform/prodOperation/announcement/logs',
        // '/api/opPlatform/prodOperation/announcement/queryAllEnv',
      ], // boolean or string[] eg: true or ['/api/*']
      urls: [
        '/manage/*',
        '/api/*',
        '/user/*',
        '/district/*',
        '/api/zoss/*',
        '/api/member/*',
        '/api/article/*',
        '/zcy/user-web/v2/*',
        '/announcement/api/*',
        '/api/apps/getAppsBasicInfo',
        '/api/district/getMyDistrict',
        '/api/users/getUserIdentity',
        '/api/apps/getAppsByDimForFront',
        '/api/privileges/getAppMenuTree',
        '/api/district/getDistrictTree',
        '/api/address/:pid/children',
        '/api/privileges/getEnvHref',
        '/api/district/getSubDistrictByPid',
        '/announcement/api/project/announcementProjectList',
        '/api/biz-bidding/doctor-manhattan/project-manage/sync-metadata',
        '/neoericBacklog/api/obtainBacklogHeadInfo',
        '/announcement/config/getDistTree',
        // '/announcement/*',
        '/announcement/config/*',
        '/zcy/*',
        '/neoericWorkflow/*',
        '/formCenter/form/common/v3/schema/*',
      ],
    },
  },
  user,
};
