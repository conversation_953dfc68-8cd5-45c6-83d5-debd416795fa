/*
 * @Description: 标准字段使用详情页 hooks
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchStandardFieldUseDetail,
  fetchDictList,
  fetchStandardFieldUseLog,
} from '../../../services';

export default function useStandardFieldUseDetailApi() {
  // 获取字典数据
  const dictRes = useRequest((params) => fetchDictList(params), { manual: true });
  const discList = dictRes?.data?.result;

  // 获取详情
  const detailRes = useRequest((params) => fetchStandardFieldUseDetail(params), { manual: true });
  const detailData = detailRes?.data?.result;

  // 日志
  const logRes = useRequest((params) => fetchStandardFieldUseLog(params), { manual: true });
  const logList = logRes?.data?.result?.data;
  const logTotal = logRes?.data?.result?.total;

  return {
    discList,
    fetchDictList: dictRes.runAsync,
    detailData: detailData || {},
    detailLoading: detailRes.loading,
    fetchDetail: detailRes.runAsync,
    logList,
    logTotal,
    fetchLog: logRes.runAsync,
  };
}
