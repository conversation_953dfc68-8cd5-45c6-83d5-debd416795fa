/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-06-05 10:49:32
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-06-05 20:24:09
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Modal, Form, FormGrid } from 'doraemon';
import { handleFormFields } from 'src/routes/StandardFieldManage/components/handleFormFields';
import {
  standardFieldUseConfig,
  ActionType,
} from '../../../config/standardFieldUseConfig';

function AddOrEditModal(props) {
  const {
    form,
    visible,
    mode,
    formDetail,
    bizType,
    allAnnTypeList,
    stdlibNamesList,
    addManage,
    editManage,
  } = props;

  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
    column: 1,
  };
  const getFormItem = (record?: any) => {
    const fields = standardFieldUseConfig({
      mode,
      bizType,
      allAnnTypeList,
      stdlibNamesList,
    });
    return handleFormFields({
      fields,
      record,
      form,
    });
  };

  const onOk = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      if (mode === ActionType.CREATE) {
        values.stdlibCodeList = values.stdlibNames;
        delete values.stdlibNames;
        addManage.run(values);
      } else if (mode === ActionType.EDIT) {
        values.stdlibCodeList = values.stdlibNames;
        delete values.stdlibNames;
        editManage.run({
          id: formDetail.id,
          forFormRender: true,
          forAnnPush: true,
          alias: values.alias,
        });
      }
    });
  };
  return (
    <Modal
      title={mode === ActionType.CREATE ? '新增' : '编辑'}
      visible={visible}
      onOk={onOk}
      destroyOnClose
      width={500}
    >
      <Form>
        <FormGrid
          {...formItemLayout}
          formGridItem={getFormItem(formDetail) || []}
        />
      </Form>
    </Modal>
  );
}

export default Form.create()(AddOrEditModal);
