/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../../ajax/opPlatform'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_2024_58021 = '' as any
const devUrl_2024_58021 = '' as any
const prodUrl_2024_58021 = '' as any
const dataKey_2024_58021 = 'data' as any

/**
 * 接口 [公告类型接口：获取当前环境下所有公告类型↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allAnnType`
 * @项目ID 2024
 */
export interface AllAnnTypeGetRequest {}

/**
 * 接口 [公告类型接口：获取当前环境下所有公告类型↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allAnnType`
 * @项目ID 2024
 */
export interface AllAnnTypeGetResponse {
  success?: boolean
  result?: {
    /**
     * 外层为公告大类id
     * 内层为具体公告类型code
     */
    id?: number
    /**
     * 外层统一为0
     * 内层为该公告类型归属的公告大类id
     */
    parentId?: number
    /**
     * 外层为公告大类名称
     * 内层为具体公告类型名称
     */
    name?: string
    /**
     * 仅外层有值，为当前公告大类下所有的公告类型，为嵌套格式
     */
    children?: {
      /**
       * 外层为公告大类id
       * 内层为具体公告类型code
       */
      id?: number
      /**
       * 外层统一为0
       * 内层为该公告类型归属的公告大类id
       */
      parentId?: number
      /**
       * 外层为公告大类名称
       * 内层为具体公告类型名称
       */
      name?: string
      /**
       * 仅外层有值，为当前公告大类下所有的公告类型，为嵌套格式
       */
      children?: {}[]
    }[]
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [公告类型接口：获取当前环境下所有公告类型↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allAnnType`
 * @项目ID 2024
 */
type AllAnnTypeGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/allAnnType',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [公告类型接口：获取当前环境下所有公告类型↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allAnnType`
 * @项目ID 2024
 */
const allAnnTypeGetRequestConfig: AllAnnTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/allAnnType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'allAnnTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告类型接口：获取当前环境下所有公告类型↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allAnnType`
 * @项目ID 2024
 */
export const allAnnTypeGet = /*#__PURE__*/ (requestData?: AllAnnTypeGetRequest, ...args: UserRequestRestArgs) => {
  return request<AllAnnTypeGetResponse>(prepare(allAnnTypeGetRequestConfig, requestData), ...args)
}

allAnnTypeGet.requestConfig = allAnnTypeGetRequestConfig

/**
 * 接口 [区划信息接口：获取当前环境下所有区划层级信息↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allDistrict`
 * @项目ID 2024
 */
export interface AllDistrictGetRequest {}

/**
 * 接口 [区划信息接口：获取当前环境下所有区划层级信息↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allDistrict`
 * @项目ID 2024
 */
export interface AllDistrictGetResponse {
  success?: boolean
  result?: {
    /**
     * 区划code
     */
    code?: string
    /**
     * 区划名称
     */
    name?: string
    /**
     * 下属区划列表
     */
    children?: {
      /**
       * 区划code
       */
      code?: string
      /**
       * 区划名称
       */
      name?: string
      /**
       * 下属区划列表
       */
      children?: {}[]
    }[]
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [区划信息接口：获取当前环境下所有区划层级信息↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allDistrict`
 * @项目ID 2024
 */
type AllDistrictGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/allDistrict',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [区划信息接口：获取当前环境下所有区划层级信息↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allDistrict`
 * @项目ID 2024
 */
const allDistrictGetRequestConfig: AllDistrictGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/allDistrict',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'allDistrictGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [区划信息接口：获取当前环境下所有区划层级信息↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/allDistrict`
 * @项目ID 2024
 */
export const allDistrictGet = /*#__PURE__*/ (requestData?: AllDistrictGetRequest, ...args: UserRequestRestArgs) => {
  return request<AllDistrictGetResponse>(prepare(allDistrictGetRequestConfig, requestData), ...args)
}

allDistrictGet.requestConfig = allDistrictGetRequestConfig

/**
 * 接口 [标准字段引用情况列表接口：获取当前环境下（区划页面还指定区划）所有引用的标准字段↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/list`
 * @项目ID 2024
 */
export interface ListGetRequest {
  /**
   * 指定某个区划
   */
  district?: string
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: string
  /**
   * 公告类型code
   */
  annType?: string
  /**
   * 标准字段code，作为额外检索条件之一
   */
  stdlibCode?: string
  /**
   * 标准字段名称，作为额外检索条件之一
   */
  stdlibName?: string
  /**
   * 当前页码
   */
  pageNo?: string
  /**
   * 每页搜索记录数
   */
  pageSize?: string
}

/**
 * 接口 [标准字段引用情况列表接口：获取当前环境下（区划页面还指定区划）所有引用的标准字段↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/list`
 * @项目ID 2024
 */
export interface ListGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 引用配置库主键id
       */
      id?: number
      /**
       * 排序序号
       */
      orderNo?: number
      /**
       * 标准字段code
       */
      stdlibCode?: string
      /**
       * 标准字段名称
       */
      stdlibName?: string
      /**
       * 标准字段控件类型，数据字典提供（componentType）
       */
      stdlibType?: number
      /**
       * 是否用于表单渲染
       */
      forFormRender?: boolean
      /**
       * 是否用于公告推送
       */
      forAnnPush?: boolean
      /**
       * 标准字段引用的别名
       */
      alias?: string
      /**
       * 更新人
       */
      modifierName?: string
      /**
       * 更新时间
       */
      modifyTime?: string
      /**
       * 按钮列表
       */
      buttonList?: {
        code?: string
        name?: string
      }[]
      /**
       * 下属子引用字段信息列表
       */
      children?: {
        /**
         * 引用配置库主键id
         */
        id?: number
        /**
         * 排序序号
         */
        orderNo?: number
        /**
         * 标准字段code
         */
        stdlibCode?: string
        /**
         * 标准字段名称
         */
        stdlibName?: string
        /**
         * 标准字段控件类型，数据字典提供（componentType）
         */
        stdlibType?: number
        /**
         * 是否用于表单渲染
         */
        forFormRender?: boolean
        /**
         * 是否用于公告推送
         */
        forAnnPush?: boolean
        /**
         * 标准字段引用的别名
         */
        alias?: string
        /**
         * 更新人
         */
        modifierName?: string
        /**
         * 更新时间
         */
        modifyTime?: string
        /**
         * 按钮列表
         */
        buttonList?: {
          code?: string
          name?: string
        }[]
        /**
         * 下属子引用字段信息列表
         */
        children?: {}[]
      }[]
    }[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [标准字段引用情况列表接口：获取当前环境下（区划页面还指定区划）所有引用的标准字段↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/list`
 * @项目ID 2024
 */
type ListGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/list',
    'data',
    string,
    'district' | 'bizType' | 'annType' | 'stdlibCode' | 'stdlibName' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [标准字段引用情况列表接口：获取当前环境下（区划页面还指定区划）所有引用的标准字段↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/list`
 * @项目ID 2024
 */
const listGetRequestConfig: ListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/list',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: ['district', 'bizType', 'annType', 'stdlibCode', 'stdlibName', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [标准字段引用情况列表接口：获取当前环境下（区划页面还指定区划）所有引用的标准字段↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/list`
 * @项目ID 2024
 */
export const listGet = /*#__PURE__*/ (requestData: ListGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListGetResponse>(prepare(listGetRequestConfig, requestData), ...args)
}

listGet.requestConfig = listGetRequestConfig

/**
 * 接口 [全量标准字段获取接口：获取当前环境和指定业务类型下的所有标准字段（只获取父级标准字段，子级标准字段不通过此接口中获取）↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib`
 * @项目ID 2024
 */
export interface GetAllStdlibGetRequest {
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: string
  /**
   * 标准字段code，作为额外检索条件之一
   */
  stdlibCode?: string
  /**
   * 标准字段名称，作为额外检索条件之一
   */
  stdlibName?: string
  /**
   * 当前页码
   */
  pageNo?: string
  /**
   * 每页搜索记录数
   */
  pageSize?: string
}

/**
 * 接口 [全量标准字段获取接口：获取当前环境和指定业务类型下的所有标准字段（只获取父级标准字段，子级标准字段不通过此接口中获取）↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib`
 * @项目ID 2024
 */
export interface GetAllStdlibGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 标准字段code
       */
      stdlibCode?: string
      /**
       * 标准字段名称
       */
      stdlibName?: string
    }[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [全量标准字段获取接口：获取当前环境和指定业务类型下的所有标准字段（只获取父级标准字段，子级标准字段不通过此接口中获取）↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib`
 * @项目ID 2024
 */
type GetAllStdlibGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib',
    'data',
    string,
    'bizType' | 'stdlibCode' | 'stdlibName' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [全量标准字段获取接口：获取当前环境和指定业务类型下的所有标准字段（只获取父级标准字段，子级标准字段不通过此接口中获取）↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib`
 * @项目ID 2024
 */
const getAllStdlibGetRequestConfig: GetAllStdlibGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: ['bizType', 'stdlibCode', 'stdlibName', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAllStdlibGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [全量标准字段获取接口：获取当前环境和指定业务类型下的所有标准字段（只获取父级标准字段，子级标准字段不通过此接口中获取）↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `GET /api/opPlatform/prodOperation/announcement/referConfig/getAllStdlib`
 * @项目ID 2024
 */
export const getAllStdlibGet = /*#__PURE__*/ (requestData: GetAllStdlibGetRequest, ...args: UserRequestRestArgs) => {
  return request<GetAllStdlibGetResponse>(prepare(getAllStdlibGetRequestConfig, requestData), ...args)
}

getAllStdlibGet.requestConfig = getAllStdlibGetRequestConfig

/**
 * 接口 [新增标准字段引用接口：增加标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/add`
 * @项目ID 2024
 */
export interface AddPostRequest {
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: number
  /**
   * 指定某个区划下引用，不指定就是绑定在某环境下
   */
  district?: string
  /**
   * 公告类型code
   */
  annType?: number
  /**
   * 待增加引用的标准字段code列表
   */
  stdlibCodeList?: string[]
  /**
   * 是否用于表单渲染
   */
  forFormRender?: boolean
  /**
   * 是否用于公告推送
   */
  forAnnPush?: boolean
}

/**
 * 接口 [新增标准字段引用接口：增加标准字段在指定环境和指定区划下的引用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/add`
 * @项目ID 2024
 */
export interface AddPostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [新增标准字段引用接口：增加标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/add`
 * @项目ID 2024
 */
type AddPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/add',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [新增标准字段引用接口：增加标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/add`
 * @项目ID 2024
 */
const addPostRequestConfig: AddPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/add',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'addPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [新增标准字段引用接口：增加标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/add`
 * @项目ID 2024
 */
export const addPost = /*#__PURE__*/ (requestData: AddPostRequest, ...args: UserRequestRestArgs) => {
  return request<AddPostResponse>(prepare(addPostRequestConfig, requestData), ...args)
}

addPost.requestConfig = addPostRequestConfig

/**
 * 接口 [启用标准字段引用接口：启用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/enable`
 * @项目ID 2024
 */
export interface EnablePostRequest {
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: number
  /**
   * 指定某个区划下引用，不指定就是绑定在某环境下
   */
  district?: string
  /**
   * 待启用引用的标准字段code
   */
  stdlibCode?: string
}

/**
 * 接口 [启用标准字段引用接口：启用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/enable`
 * @项目ID 2024
 */
export interface EnablePostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [启用标准字段引用接口：启用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/enable`
 * @项目ID 2024
 */
type EnablePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/enable',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [启用标准字段引用接口：启用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/enable`
 * @项目ID 2024
 */
const enablePostRequestConfig: EnablePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/enable',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'enablePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [启用标准字段引用接口：启用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/enable`
 * @项目ID 2024
 */
export const enablePost = /*#__PURE__*/ (requestData: EnablePostRequest, ...args: UserRequestRestArgs) => {
  return request<EnablePostResponse>(prepare(enablePostRequestConfig, requestData), ...args)
}

enablePost.requestConfig = enablePostRequestConfig

/**
 * 接口 [停用标准字段引用接口：停用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/suspend`
 * @项目ID 2024
 */
export interface SuspendPostRequest {
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: number
  /**
   * 指定某个区划下引用，不指定就是绑定在某环境下
   */
  district?: string
  /**
   * 待停用引用的标准字段code
   */
  stdlibCode?: string
}

/**
 * 接口 [停用标准字段引用接口：停用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/suspend`
 * @项目ID 2024
 */
export interface SuspendPostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [停用标准字段引用接口：停用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/suspend`
 * @项目ID 2024
 */
type SuspendPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/suspend',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [停用标准字段引用接口：停用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/suspend`
 * @项目ID 2024
 */
const suspendPostRequestConfig: SuspendPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/suspend',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'suspendPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [停用标准字段引用接口：停用指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/suspend`
 * @项目ID 2024
 */
export const suspendPost = /*#__PURE__*/ (requestData: SuspendPostRequest, ...args: UserRequestRestArgs) => {
  return request<SuspendPostResponse>(prepare(suspendPostRequestConfig, requestData), ...args)
}

suspendPost.requestConfig = suspendPostRequestConfig

/**
 * 接口 [删除标准字段引用接口：删除指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/delete`
 * @项目ID 2024
 */
export interface DeletePostRequest {
  /**
   * 引用配置库主键id
   */
  id?: number
  /**
   * 业务类型，默认为1（政采）
   */
  bizType?: number
  /**
   * 指定某个区划下引用，不指定就是绑定在某环境下
   */
  district?: string
  /**
   * 指定公告类型code
   */
  annType?: number
}

/**
 * 接口 [删除标准字段引用接口：删除指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/delete`
 * @项目ID 2024
 */
export interface DeletePostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [删除标准字段引用接口：删除指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/delete`
 * @项目ID 2024
 */
type DeletePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/delete',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [删除标准字段引用接口：删除指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/delete`
 * @项目ID 2024
 */
const deletePostRequestConfig: DeletePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/delete',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deletePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除标准字段引用接口：删除指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/delete`
 * @项目ID 2024
 */
export const deletePost = /*#__PURE__*/ (requestData: DeletePostRequest, ...args: UserRequestRestArgs) => {
  return request<DeletePostResponse>(prepare(deletePostRequestConfig, requestData), ...args)
}

deletePost.requestConfig = deletePostRequestConfig

/**
 * 接口 [编辑标准字段引用接口：编辑指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/edit`
 * @项目ID 2024
 */
export interface EditPostRequest {
  /**
   * 引用配置库主键id
   */
  id?: number
  /**
   * 是否用于表单渲染
   */
  forFormRender?: boolean
  /**
   * 是否用于公告推送
   */
  forAnnPush?: boolean
  /**
   * 标准字段引用的别名
   */
  alias?: string
}

/**
 * 接口 [编辑标准字段引用接口：编辑指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/edit`
 * @项目ID 2024
 */
export interface EditPostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [编辑标准字段引用接口：编辑指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/edit`
 * @项目ID 2024
 */
type EditPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/edit',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [编辑标准字段引用接口：编辑指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/edit`
 * @项目ID 2024
 */
const editPostRequestConfig: EditPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/edit',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'editPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑标准字段引用接口：编辑指定某个标准字段在指定环境和指定区划下的引用↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/edit`
 * @项目ID 2024
 */
export const editPost = /*#__PURE__*/ (requestData: EditPostRequest, ...args: UserRequestRestArgs) => {
  return request<EditPostResponse>(prepare(editPostRequestConfig, requestData), ...args)
}

editPost.requestConfig = editPostRequestConfig

/**
 * 接口 [全量复制标准字段引用接口：将当前环境下指定区划指定公告类型的标准字段引用全量复制到同一环境下指定区划指定公告类型下↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/copy`
 * @项目ID 2024
 */
export interface CopyPostRequest {
  /**
   * 指定业务类型
   */
  sourceBizType?: number
  /**
   * 指定某个区划下引用，不指定就是绑定在某环境下
   */
  sourceDistrict?: string
  /**
   * 指定公告类型code
   */
  sourceAnnType?: number
  /**
   * 目标业务类型
   */
  targetBizType?: number
  /**
   * 指定要复制到的区划，不指定就是指定在targetEnv环境下
   */
  targetDistrict?: string
  /**
   * 目标公告类型code
   */
  targetAnnType?: number
}

/**
 * 接口 [全量复制标准字段引用接口：将当前环境下指定区划指定公告类型的标准字段引用全量复制到同一环境下指定区划指定公告类型下↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/copy`
 * @项目ID 2024
 */
export interface CopyPostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [全量复制标准字段引用接口：将当前环境下指定区划指定公告类型的标准字段引用全量复制到同一环境下指定区划指定公告类型下↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/copy`
 * @项目ID 2024
 */
type CopyPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/copy',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [全量复制标准字段引用接口：将当前环境下指定区划指定公告类型的标准字段引用全量复制到同一环境下指定区划指定公告类型下↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/copy`
 * @项目ID 2024
 */
const copyPostRequestConfig: CopyPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/copy',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'copyPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [全量复制标准字段引用接口：将当前环境下指定区划指定公告类型的标准字段引用全量复制到同一环境下指定区划指定公告类型下↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/copy`
 * @项目ID 2024
 */
export const copyPost = /*#__PURE__*/ (requestData: CopyPostRequest, ...args: UserRequestRestArgs) => {
  return request<CopyPostResponse>(prepare(copyPostRequestConfig, requestData), ...args)
}

copyPost.requestConfig = copyPostRequestConfig

/**
 * 接口 [移动标准字段引用位置接口：前端将整体移动后的引用记录id列表传入，便于整体排序↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/move`
 * @项目ID 2024
 */
export interface MovePostRequest {
  /**
   * 排序后的标准字段引用id列表，索引index即为顺序
   */
  idList?: number[]
}

/**
 * 接口 [移动标准字段引用位置接口：前端将整体移动后的引用记录id列表传入，便于整体排序↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/move`
 * @项目ID 2024
 */
export interface MovePostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [移动标准字段引用位置接口：前端将整体移动后的引用记录id列表传入，便于整体排序↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/move`
 * @项目ID 2024
 */
type MovePostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/opPlatform/prodOperation/announcement/referConfig/move',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [移动标准字段引用位置接口：前端将整体移动后的引用记录id列表传入，便于整体排序↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/move`
 * @项目ID 2024
 */
const movePostRequestConfig: MovePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_2024_58021,
  devUrl: devUrl_2024_58021,
  prodUrl: prodUrl_2024_58021,
  path: '/api/opPlatform/prodOperation/announcement/referConfig/move',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_2024_58021,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'movePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [移动标准字段引用位置接口：前端将整体移动后的引用记录id列表传入，便于整体排序↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReferConfigController↗](undefined)
 * @请求头 `POST /api/opPlatform/prodOperation/announcement/referConfig/move`
 * @项目ID 2024
 */
export const movePost = /*#__PURE__*/ (requestData: MovePostRequest, ...args: UserRequestRestArgs) => {
  return request<MovePostResponse>(prepare(movePostRequestConfig, requestData), ...args)
}

movePost.requestConfig = movePostRequestConfig

/* prettier-ignore-end */
