const packageName = require('./package.json').name.replace('-', '_');

const path = require('path');

function resolve(dir) {
  return path.join(__dirname, '', dir);
}

module.exports = {
  assetsDir: 'announcement-static',
  lintOnSave: false,
  webpack: {
    configureWebpack: {
      output: {
        library: `${packageName}-[name]`,
        libraryTarget: 'umd',
        jsonpFunction: `webpackJsonp_${packageName}`,
      },
      devtool: 'source-map',
      resolve: {
        alias: {
          doraemon: '@zcy/doraemon',
          components: 'src/components',
          utils: resolve('src/utils'),
        },
        extensions: ['.d.ts', '.tsx', '.ts', '.js', '.jsx'],
      },
      module: {
        rules: [
          {
            test: /\.(js|jsx|ts|tsx)?$/,
            use: {
              loader: 'babel-loader',
              options: {
                presets: [
                  '@babel/preset-env',
                  '@babel/preset-react',
                  '@babel/preset-typescript',
                ],
                plugins: [
                     
                  [
                    '@babel/plugin-proposal-decorators',
                    {
                      legacy: true,
                    },
                  ],
                  ['@babel/plugin-proposal-export-default-from'],
                  ['@babel/plugin-transform-runtime'],
                  ['@babel/plugin-transform-modules-commonjs'],
                  [
                    'import',
                    {
                      libraryName: 'doraemon',
                      style: true,
                          
                    },
                  ],
                  ['@zcy/babel-plugin-compatible-object-destructuring'],
                  [
                    'import',
                    {  
                      libraryName: '@zcy/doraemon',    
                      style: true,
                    },
                    'import @zcy/doraemon',
                  ],
                ],
              },
            },
            exclude: /node_modules/,
          },
        ],
      },  
    },
  },
  babel: {
    modules: false,
    import: [{
      libraryName: 'doraemon',
      style: true,
    }],
    plugins: [
      'babel-plugin-dva-hmr',
    ],
  },
};
